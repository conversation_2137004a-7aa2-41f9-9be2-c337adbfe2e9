/**
 * Configuration System Tests
 * 
 * Tests for the configuration loading, saving, and validation system.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { existsSync, writeFileSync, rmSync } from 'fs';
import { join } from 'path';
import { loadConfig, saveConfig, getConfigPath, validateConfig } from '../../src/utils/config.js';
import { testUtils } from '../setup.js';
import type { AppConfig } from '../../src/types/index.js';

describe('Configuration System', () => {
  let testConfigPath: string;

  beforeEach(() => {
    testUtils.cleanup();
    testConfigPath = join(testUtils.getTestConfigDir(), 'config.json');
  });

  afterEach(() => {
    if (existsSync(testConfigPath)) {
      rmSync(testConfigPath, { force: true });
    }
  });

  describe('loadConfig', () => {
    it('should load default config when no config file exists', () => {
      const config = loadConfig();
      
      expect(config).toBeDefined();
      expect(config.model).toBe('gpt-4');
      expect(config.provider).toBe('openai');
      expect(config.approvalMode).toBe('auto');
    });

    it('should load config from file when it exists', () => {
      const testConfig: Partial<AppConfig> = {
        model: 'gpt-3.5-turbo',
        provider: 'openai',
        approvalMode: 'manual',
        workingDirectory: '/test/dir',
      };

      writeFileSync(testConfigPath, JSON.stringify(testConfig, null, 2));
      
      const config = loadConfig();
      
      expect(config.model).toBe('gpt-3.5-turbo');
      expect(config.approvalMode).toBe('manual');
      expect(config.workingDirectory).toBe('/test/dir');
    });

    it('should merge with defaults for partial config', () => {
      const partialConfig = {
        model: 'claude-3-sonnet',
      };

      writeFileSync(testConfigPath, JSON.stringify(partialConfig, null, 2));
      
      const config = loadConfig();
      
      expect(config.model).toBe('claude-3-sonnet');
      expect(config.provider).toBe('openai'); // Should use default
      expect(config.approvalMode).toBe('auto'); // Should use default
    });

    it('should handle invalid JSON gracefully', () => {
      writeFileSync(testConfigPath, 'invalid json content');
      
      const config = loadConfig();
      
      // Should return default config
      expect(config.model).toBe('gpt-4');
      expect(config.provider).toBe('openai');
    });
  });

  describe('saveConfig', () => {
    it('should save config to file', () => {
      const config: Partial<AppConfig> = {
        model: 'gpt-4-turbo',
        provider: 'openai',
        approvalMode: 'manual',
        workingDirectory: '/custom/dir',
      };

      saveConfig(config);
      
      expect(existsSync(testConfigPath)).toBe(true);
      
      const savedConfig = loadConfig();
      expect(savedConfig.model).toBe('gpt-4-turbo');
      expect(savedConfig.approvalMode).toBe('manual');
      expect(savedConfig.workingDirectory).toBe('/custom/dir');
    });

    it('should create config directory if it does not exist', () => {
      const configDir = testUtils.getTestConfigDir();
      if (existsSync(configDir)) {
        rmSync(configDir, { recursive: true, force: true });
      }

      const config: Partial<AppConfig> = {
        model: 'gpt-4',
      };

      saveConfig(config);
      
      expect(existsSync(configDir)).toBe(true);
      expect(existsSync(testConfigPath)).toBe(true);
    });

    it('should merge with existing config', () => {
      // Save initial config
      const initialConfig: Partial<AppConfig> = {
        model: 'gpt-4',
        provider: 'openai',
        approvalMode: 'auto',
      };
      saveConfig(initialConfig);

      // Update with partial config
      const updateConfig: Partial<AppConfig> = {
        model: 'gpt-4-turbo',
        approvalMode: 'manual',
      };
      saveConfig(updateConfig);

      const finalConfig = loadConfig();
      expect(finalConfig.model).toBe('gpt-4-turbo');
      expect(finalConfig.provider).toBe('openai'); // Should be preserved
      expect(finalConfig.approvalMode).toBe('manual');
    });
  });

  describe('getConfigPath', () => {
    it('should return correct config path', () => {
      const configPath = getConfigPath();
      expect(configPath).toContain('config.json');
      expect(configPath).toContain('.kritrima-ai');
    });
  });

  describe('validateConfig', () => {
    it('should validate correct config', () => {
      const validConfig: AppConfig = {
        model: 'gpt-4',
        provider: 'openai',
        approvalMode: 'auto',
        workingDirectory: process.cwd(),
        timeout: 30000,
        maxIterations: 10,
        debug: false,
        notifications: true,
      };

      const result = validateConfig(validConfig);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid model', () => {
      const invalidConfig = {
        model: 'invalid-model',
        provider: 'openai',
        approvalMode: 'auto',
      } as AppConfig;

      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('model'))).toBe(true);
    });

    it('should detect invalid provider', () => {
      const invalidConfig = {
        model: 'gpt-4',
        provider: 'invalid-provider',
        approvalMode: 'auto',
      } as AppConfig;

      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('provider'))).toBe(true);
    });

    it('should detect invalid approval mode', () => {
      const invalidConfig = {
        model: 'gpt-4',
        provider: 'openai',
        approvalMode: 'invalid-mode',
      } as AppConfig;

      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('approval'))).toBe(true);
    });

    it('should detect invalid timeout', () => {
      const invalidConfig = {
        model: 'gpt-4',
        provider: 'openai',
        approvalMode: 'auto',
        timeout: -1000,
      } as AppConfig;

      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('timeout'))).toBe(true);
    });

    it('should detect invalid max iterations', () => {
      const invalidConfig = {
        model: 'gpt-4',
        provider: 'openai',
        approvalMode: 'auto',
        maxIterations: 0,
      } as AppConfig;

      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors.some(error => error.includes('iterations'))).toBe(true);
    });
  });

  describe('Environment Variables', () => {
    it('should respect environment variable overrides', () => {
      process.env.KRITRIMA_MODEL = 'gpt-3.5-turbo';
      process.env.KRITRIMA_PROVIDER = 'anthropic';
      
      const config = loadConfig();
      
      expect(config.model).toBe('gpt-3.5-turbo');
      expect(config.provider).toBe('anthropic');
      
      // Clean up
      delete process.env.KRITRIMA_MODEL;
      delete process.env.KRITRIMA_PROVIDER;
    });

    it('should handle boolean environment variables', () => {
      process.env.KRITRIMA_DEBUG = 'true';
      process.env.KRITRIMA_NOTIFICATIONS = 'false';
      
      const config = loadConfig();
      
      expect(config.debug).toBe(true);
      expect(config.notifications).toBe(false);
      
      // Clean up
      delete process.env.KRITRIMA_DEBUG;
      delete process.env.KRITRIMA_NOTIFICATIONS;
    });

    it('should handle numeric environment variables', () => {
      process.env.KRITRIMA_TIMEOUT = '60000';
      process.env.KRITRIMA_MAX_ITERATIONS = '20';
      
      const config = loadConfig();
      
      expect(config.timeout).toBe(60000);
      expect(config.maxIterations).toBe(20);
      
      // Clean up
      delete process.env.KRITRIMA_TIMEOUT;
      delete process.env.KRITRIMA_MAX_ITERATIONS;
    });
  });
});
