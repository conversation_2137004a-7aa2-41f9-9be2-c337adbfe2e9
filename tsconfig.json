{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "downlevelIteration": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "skipLibCheck": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "jsx": "react-jsx", "lib": ["ES2022", "DOM"], "types": ["node"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/utils/*": ["src/utils/*"], "@/hooks/*": ["src/hooks/*"], "@/types/*": ["src/types/*"]}}, "include": ["src/**/*", "bin/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}