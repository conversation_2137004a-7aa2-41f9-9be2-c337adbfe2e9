/**
 * File Tag Utilities Tests
 * 
 * Tests for file tag expansion and XML block processing.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';
import { 
  expandFileTags, 
  collapseXmlBlocks, 
  extractFilePaths, 
  extractFilePathsFromXml,
  validateFileTags,
  getFileSuggestions 
} from '../../src/utils/file-tag-utils.js';
import { testUtils } from '../setup.js';

describe('File Tag Utilities', () => {
  let testDir: string;

  beforeEach(() => {
    testUtils.cleanup();
    testDir = testUtils.getTestDir();
  });

  afterEach(() => {
    testUtils.cleanup();
  });

  describe('expandFileTags', () => {
    it('should expand single file tag to XML block', async () => {
      const testFile = 'test.txt';
      const testContent = 'Hello, world!';
      testUtils.createTestFile(testFile, testContent);

      const input = `Please review @${testFile}`;
      const result = await expandFileTags(input, testDir);

      expect(result).toContain('<file path="test.txt">');
      expect(result).toContain('Hello, world!');
      expect(result).toContain('</file>');
      expect(result).not.toContain('@test.txt');
    });

    it('should expand multiple file tags', async () => {
      testUtils.createTestFile('file1.txt', 'Content 1');
      testUtils.createTestFile('file2.txt', 'Content 2');

      const input = 'Review @file1.txt and @file2.txt';
      const result = await expandFileTags(input, testDir);

      expect(result).toContain('<file path="file1.txt">');
      expect(result).toContain('Content 1');
      expect(result).toContain('<file path="file2.txt">');
      expect(result).toContain('Content 2');
    });

    it('should handle non-existent files gracefully', async () => {
      const input = 'Review @nonexistent.txt';
      const result = await expandFileTags(input, testDir);

      expect(result).toContain('<file path="nonexistent.txt" error="true">');
      expect(result).toContain('Error: File not found');
    });

    it('should handle files in subdirectories', async () => {
      const subDir = 'subdir';
      testUtils.createTestDirectory(subDir);
      const filePath = join(subDir, 'nested.txt');
      testUtils.createTestFile(filePath, 'Nested content');

      const input = `Review @${subDir}/nested.txt`;
      const result = await expandFileTags(input, testDir);

      expect(result).toContain(`<file path="${subDir}/nested.txt">`);
      expect(result).toContain('Nested content');
    });

    it('should escape XML characters in content', async () => {
      const testContent = 'Content with <tags> & "quotes" and \'apostrophes\'';
      testUtils.createTestFile('test.txt', testContent);

      const input = 'Review @test.txt';
      const result = await expandFileTags(input, testDir);

      expect(result).toContain('&lt;tags&gt;');
      expect(result).toContain('&amp;');
      expect(result).toContain('&quot;quotes&quot;');
      expect(result).toContain('&#39;apostrophes&#39;');
    });

    it('should handle large files with size limit', async () => {
      const largeContent = 'x'.repeat(2 * 1024 * 1024); // 2MB
      testUtils.createTestFile('large.txt', largeContent);

      const input = 'Review @large.txt';
      const result = await expandFileTags(input, testDir);

      expect(result).toContain('error="true"');
      expect(result).toContain('File too large');
    });
  });

  describe('collapseXmlBlocks', () => {
    it('should collapse XML blocks back to file tags', () => {
      const input = `
        <file path="test.txt">
        Hello, world!
        </file>
      `;
      
      const result = collapseXmlBlocks(input);
      expect(result).toContain('@test.txt');
      expect(result).not.toContain('<file path="test.txt">');
    });

    it('should collapse multiple XML blocks', () => {
      const input = `
        <file path="file1.txt">Content 1</file>
        <file path="file2.txt">Content 2</file>
      `;
      
      const result = collapseXmlBlocks(input);
      expect(result).toContain('@file1.txt');
      expect(result).toContain('@file2.txt');
    });

    it('should handle nested paths in XML blocks', () => {
      const input = '<file path="src/utils/helper.ts">export function helper() {}</file>';
      const result = collapseXmlBlocks(input);
      expect(result).toContain('@src/utils/helper.ts');
    });
  });

  describe('extractFilePaths', () => {
    it('should extract file paths from text', () => {
      const text = 'Review @file1.txt and @src/file2.js';
      const paths = extractFilePaths(text);
      
      expect(paths).toEqual(['file1.txt', 'src/file2.js']);
    });

    it('should handle text without file tags', () => {
      const text = 'No file tags here';
      const paths = extractFilePaths(text);
      
      expect(paths).toEqual([]);
    });

    it('should handle duplicate file paths', () => {
      const text = 'Review @file.txt and then @file.txt again';
      const paths = extractFilePaths(text);
      
      expect(paths).toEqual(['file.txt', 'file.txt']);
    });
  });

  describe('extractFilePathsFromXml', () => {
    it('should extract file paths from XML blocks', () => {
      const text = `
        <file path="file1.txt">Content 1</file>
        <file path="src/file2.js">Content 2</file>
      `;
      
      const paths = extractFilePathsFromXml(text);
      expect(paths).toEqual(['file1.txt', 'src/file2.js']);
    });

    it('should handle text without XML blocks', () => {
      const text = 'No XML blocks here';
      const paths = extractFilePathsFromXml(text);
      
      expect(paths).toEqual([]);
    });
  });

  describe('validateFileTags', () => {
    it('should validate existing files', () => {
      testUtils.createTestFile('valid.txt', 'Valid content');
      
      const text = 'Review @valid.txt';
      const result = validateFileTags(text, testDir);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect non-existent files', () => {
      const text = 'Review @nonexistent.txt';
      const result = validateFileTags(text, testDir);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].filePath).toBe('nonexistent.txt');
      expect(result.errors[0].error).toBe('File not found');
    });

    it('should detect directories instead of files', () => {
      testUtils.createTestDirectory('testdir');
      
      const text = 'Review @testdir';
      const result = validateFileTags(text, testDir);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0].error).toBe('Not a file');
    });

    it('should detect files that are too large', () => {
      const largeContent = 'x'.repeat(2 * 1024 * 1024); // 2MB
      testUtils.createTestFile('large.txt', largeContent);
      
      const text = 'Review @large.txt';
      const result = validateFileTags(text, testDir);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0].error).toContain('File too large');
    });

    it('should validate multiple files', () => {
      testUtils.createTestFile('valid1.txt', 'Content 1');
      testUtils.createTestFile('valid2.txt', 'Content 2');
      
      const text = 'Review @valid1.txt and @valid2.txt and @nonexistent.txt';
      const result = validateFileTags(text, testDir);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].filePath).toBe('nonexistent.txt');
    });
  });

  describe('getFileSuggestions', () => {
    beforeEach(() => {
      // Create test file structure
      testUtils.createTestFile('readme.md', 'Readme content');
      testUtils.createTestFile('package.json', '{}');
      testUtils.createTestDirectory('src');
      testUtils.createTestFile('src/index.ts', 'export {}');
      testUtils.createTestFile('src/utils.ts', 'export {}');
      testUtils.createTestDirectory('tests');
      testUtils.createTestFile('tests/test.spec.ts', 'test content');
    });

    it('should suggest files based on partial input', () => {
      const suggestions = getFileSuggestions('Review @read', testDir);
      
      expect(suggestions).toContain('readme.md');
    });

    it('should suggest files in subdirectories', () => {
      const suggestions = getFileSuggestions('Review @src/', testDir);
      
      expect(suggestions.some(s => s.includes('index.ts'))).toBe(true);
      expect(suggestions.some(s => s.includes('utils.ts'))).toBe(true);
    });

    it('should limit number of suggestions', () => {
      const suggestions = getFileSuggestions('Review @', testDir, 3);
      
      expect(suggestions.length).toBeLessThanOrEqual(3);
    });

    it('should handle empty input gracefully', () => {
      const suggestions = getFileSuggestions('', testDir);
      
      expect(suggestions).toEqual([]);
    });

    it('should handle non-existent directory gracefully', () => {
      const suggestions = getFileSuggestions('Review @nonexistent/', testDir);
      
      expect(suggestions).toEqual([]);
    });
  });
});
