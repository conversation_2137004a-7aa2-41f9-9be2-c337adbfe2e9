/**
 * Test Setup Configuration
 * 
 * Global test setup for vitest with environment configuration
 * and common utilities for testing the CLI application.
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { mkdirSync, rmSync, existsSync } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';

// Test environment configuration
const TEST_DIR = join(tmpdir(), 'kritrima-ai-cli-tests');
const TEST_CONFIG_DIR = join(TEST_DIR, '.kritrima-ai');

// Global test setup
beforeAll(() => {
  // Create test directories
  if (!existsSync(TEST_DIR)) {
    mkdirSync(TEST_DIR, { recursive: true });
  }
  
  if (!existsSync(TEST_CONFIG_DIR)) {
    mkdirSync(TEST_CONFIG_DIR, { recursive: true });
  }

  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.KRITRIMA_CONFIG_DIR = TEST_CONFIG_DIR;
  process.env.KRITRIMA_TEST_MODE = 'true';
  
  // Disable notifications during tests
  process.env.KRITRIMA_NOTIFICATIONS_ENABLED = 'false';
  
  // Disable update checks during tests
  process.env.KRITRIMA_UPDATE_CHECK_DISABLED = 'true';
});

// Global test cleanup
afterAll(() => {
  // Clean up test directories
  if (existsSync(TEST_DIR)) {
    rmSync(TEST_DIR, { recursive: true, force: true });
  }
});

// Per-test setup
beforeEach(() => {
  // Reset any global state
  jest.clearAllMocks?.();
});

// Per-test cleanup
afterEach(() => {
  // Clean up any test-specific state
});

// Test utilities
export const testUtils = {
  /**
   * Get test directory path
   */
  getTestDir(): string {
    return TEST_DIR;
  },

  /**
   * Get test config directory path
   */
  getTestConfigDir(): string {
    return TEST_CONFIG_DIR;
  },

  /**
   * Create a temporary test file
   */
  createTestFile(filename: string, content: string): string {
    const filePath = join(TEST_DIR, filename);
    const fs = require('fs');
    fs.writeFileSync(filePath, content);
    return filePath;
  },

  /**
   * Create a temporary test directory
   */
  createTestDirectory(dirname: string): string {
    const dirPath = join(TEST_DIR, dirname);
    mkdirSync(dirPath, { recursive: true });
    return dirPath;
  },

  /**
   * Clean up test files
   */
  cleanup(): void {
    if (existsSync(TEST_DIR)) {
      rmSync(TEST_DIR, { recursive: true, force: true });
      mkdirSync(TEST_DIR, { recursive: true });
    }
  },

  /**
   * Mock API responses
   */
  mockApiResponse(response: any): void {
    // Mock implementation for API responses
    global.mockApiResponse = response;
  },

  /**
   * Wait for async operations
   */
  async waitFor(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Capture console output
   */
  captureConsole(): {
    logs: string[];
    errors: string[];
    restore: () => void;
  } {
    const logs: string[] = [];
    const errors: string[] = [];
    
    const originalLog = console.log;
    const originalError = console.error;
    
    console.log = (...args) => {
      logs.push(args.join(' '));
    };
    
    console.error = (...args) => {
      errors.push(args.join(' '));
    };
    
    return {
      logs,
      errors,
      restore: () => {
        console.log = originalLog;
        console.error = originalError;
      },
    };
  },
};

// Export test environment info
export const testEnv = {
  isTest: true,
  testDir: TEST_DIR,
  configDir: TEST_CONFIG_DIR,
};

export default testUtils;
