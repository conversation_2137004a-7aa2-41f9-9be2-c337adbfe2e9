/**
 * Vendor Components Index
 * 
 * Enhanced blessed.js components with additional functionality
 * for better integration and user experience.
 */

export { EnhancedScreen, type ScreenOptions } from './screen.js';
export { EnhancedList, type ListItem, type EnhancedListOptions } from './list.js';
export { EnhancedBox, type EnhancedBoxOptions } from './box.js';

// Re-export commonly used blessed types for convenience
import type { Widgets } from 'blessed';

export type BlessedScreen = Widgets.Screen;
export type BlessedBox = Widgets.BoxElement;
export type BlessedList = Widgets.ListElement;
export type BlessedTextarea = Widgets.TextareaElement;
export type BlessedTextbox = Widgets.TextboxElement;
export type BlessedButton = Widgets.ButtonElement;
export type BlessedForm = Widgets.FormElement<any>;
export type KeyEvent = Widgets.Events.IKeyEventArg;
