/**
 * Agent Loop Core
 * 
 * The heart of the AI system implementing sophisticated autonomous workflow
 * with tool calling, conversation state management, and streaming responses.
 */

import { EventEmitter } from 'events';
import { createOpenAIClient } from '../openai-client.js';
import { streamResponses, createResponse } from '../responses.js';
import { logInfo, logError, logDebug, createTimer } from '../logger/log.js';
import { handleExecCommand } from './handle-exec-command.js';
import { applyPatch } from './apply-patch.js';
import type { 
  AgentLoopConfig, 
  ResponseInputItem, 
  ResponseOutputItem, 
  ResponseFunctionToolCall,
  FunctionTool,
  ApprovalPolicy,
  ExecInput,
  ExecResult 
} from '../../types/index.js';
import { nanoid } from 'nanoid';

/**
 * Shell function tool definition
 */
const shellFunctionTool: FunctionTool = {
  type: 'function',
  name: 'shell',
  description: 'Runs a shell command and returns its output. Use this to execute system commands, run scripts, or interact with the file system.',
  parameters: {
    type: 'object',
    properties: {
      command: {
        type: 'array',
        items: { type: 'string' },
        description: 'The command to run as an array of strings (command and arguments)',
      },
      workdir: {
        type: 'string',
        description: 'Working directory for the command (optional)',
      },
      timeout: {
        type: 'number',
        description: 'Timeout in milliseconds (optional, default: 30000)',
      },
    },
    required: ['command'],
  },
};

/**
 * Local shell function tool definition
 */
const localShellFunctionTool: FunctionTool = {
  type: 'function',
  name: 'local_shell',
  description: 'Executes a shell command directly in the local environment. Use for quick commands and file operations.',
  parameters: {
    type: 'object',
    properties: {
      command: {
        type: 'array',
        items: { type: 'string' },
        description: 'The command to run as an array of strings',
      },
      workdir: {
        type: 'string',
        description: 'Working directory for the command (optional)',
      },
    },
    required: ['command'],
  },
};

/**
 * Agent Loop implementation
 */
export class AgentLoop extends EventEmitter {
  private model: string;
  private provider: string;
  private approvalPolicy: ApprovalPolicy;
  private client: any;
  private transcript: ResponseInputItem[] = [];
  private pendingAborts = new Set<string>();
  private cumulativeThinkingMs = 0;
  private maxIterations: number;
  private timeout: number;
  private debug: boolean;

  constructor(config: AgentLoopConfig) {
    super();
    
    this.model = config.model;
    this.provider = config.provider;
    this.approvalPolicy = config.approvalPolicy;
    this.maxIterations = config.maxIterations || 10;
    this.timeout = config.timeout || 30000;
    this.debug = config.debug || false;

    // Create OpenAI client
    this.client = createOpenAIClient({
      provider: this.provider,
      timeout: this.timeout,
    });

    logInfo(`Agent loop initialized: ${this.provider}/${this.model}`);
  }

  /**
   * Process user input through the agent loop
   */
  public async processInput(input: ResponseInputItem): Promise<ResponseOutputItem> {
    const timer = createTimer('agent-loop-process');
    
    try {
      logInfo('Processing user input through agent loop');
      
      // Add input to transcript
      this.transcript.push(input);

      // Build conversation context
      const messages = this.buildConversationContext();

      // Get available tools based on approval policy
      const tools = this.getAvailableTools();

      // Create AI request
      const requestInput = {
        messages,
        model: this.model,
        provider: this.provider,
        maxTokens: 4096,
        temperature: 0.7,
        tools: tools.length > 0 ? tools.map(tool => ({
          type: 'function' as const,
          function: {
            name: tool.name,
            description: tool.description,
            parameters: tool.parameters,
          },
        })) : undefined,
        stream: true,
      };

      // Process through AI with streaming
      let response: ResponseOutputItem;
      
      if (requestInput.stream) {
        response = await this.processStreamingResponse(requestInput);
      } else {
        response = await createResponse(requestInput, this.client);
      }

      // Handle function calls if present
      if (this.hasFunctionCalls(response)) {
        response = await this.processFunctionCalls(response);
      }

      // Add response to transcript
      this.addResponseToTranscript(response);

      timer.end();
      return response;

    } catch (error) {
      timer.end();
      logError('Agent loop processing failed', error);
      throw error;
    }
  }

  /**
   * Process streaming response
   */
  private async processStreamingResponse(requestInput: any): Promise<ResponseOutputItem> {
    try {
      const completion = await this.client.chat.completions.create({
        ...requestInput,
        tools: requestInput.tools,
        stream: true,
      });

      let finalResponse: ResponseOutputItem | null = null;

      // Process streaming events
      for await (const event of streamResponses(requestInput, completion)) {
        this.emit('response-event', event);

        if (event.type === 'response.completed') {
          finalResponse = event.data.response;
          this.cumulativeThinkingMs += event.data.duration;
        }
      }

      if (!finalResponse) {
        throw new Error('No response received from streaming');
      }

      return finalResponse;

    } catch (error) {
      logError('Streaming response processing failed', error);
      throw error;
    }
  }

  /**
   * Build conversation context from transcript
   */
  private buildConversationContext(): any[] {
    return this.transcript.map(item => ({
      role: item.role,
      content: item.content.map(c => {
        if (c.type === 'input_text') {
          return { type: 'text', text: c.text || '' };
        } else if (c.type === 'input_image' && c.image) {
          return {
            type: 'image_url',
            image_url: { url: c.image.data || c.image.url || '' },
          };
        }
        return { type: 'text', text: '' };
      }),
    }));
  }

  /**
   * Get available tools based on approval policy
   */
  private getAvailableTools(): FunctionTool[] {
    const tools: FunctionTool[] = [];

    // Add shell tools based on approval policy
    if (this.approvalPolicy !== 'suggest') {
      tools.push(shellFunctionTool);
      tools.push(localShellFunctionTool);
    }

    return tools;
  }

  /**
   * Check if response has function calls
   */
  private hasFunctionCalls(response: ResponseOutputItem): boolean {
    return response.content.some(c => c.type === 'function_call');
  }

  /**
   * Process function calls in response
   */
  private async processFunctionCalls(response: ResponseOutputItem): Promise<ResponseOutputItem> {
    const functionCalls = response.content.filter(c => c.type === 'function_call' && c.functionCall);
    
    if (functionCalls.length === 0) {
      return response;
    }

    logInfo(`Processing ${functionCalls.length} function calls`);

    // Process each function call
    for (const callContent of functionCalls) {
      if (callContent.functionCall) {
        await this.handleFunctionCall(callContent.functionCall);
      }
    }

    return response;
  }

  /**
   * Handle individual function call
   */
  private async handleFunctionCall(functionCall: ResponseFunctionToolCall): Promise<void> {
    const { name, arguments: argsString } = functionCall;
    
    try {
      logDebug(`Executing function call: ${name}`);
      
      const args = JSON.parse(argsString);

      switch (name) {
        case 'shell':
        case 'local_shell':
          await this.handleShellCommand(args);
          break;
        default:
          logError(`Unknown function: ${name}`);
      }

    } catch (error) {
      logError(`Function call failed: ${name}`, error);
    }
  }

  /**
   * Handle shell command execution
   */
  private async handleShellCommand(args: any): Promise<void> {
    try {
      const execInput: ExecInput = {
        command: args.command,
        workdir: args.workdir || process.cwd(),
        timeout: args.timeout || 30000,
      };

      // Execute command through handler
      const result = await handleExecCommand(execInput, {
        approvalPolicy: this.approvalPolicy,
        workingDirectory: execInput.workdir,
      });

      logInfo(`Shell command executed: ${execInput.command.join(' ')}`);
      logDebug(`Command result: ${result.success ? 'success' : 'failed'}`);

    } catch (error) {
      logError('Shell command execution failed', error);
    }
  }

  /**
   * Add response to transcript
   */
  private addResponseToTranscript(response: ResponseOutputItem): void {
    // Convert response to input item format for transcript
    const transcriptItem: ResponseInputItem = {
      role: 'assistant',
      content: response.content.map(c => {
        if (c.type === 'output_text') {
          return { type: 'input_text', text: c.text || '' };
        }
        return { type: 'input_text', text: '' };
      }),
      type: 'message',
      id: response.id,
      timestamp: response.timestamp,
    };

    this.transcript.push(transcriptItem);
  }

  /**
   * Update model configuration
   */
  public updateModel(model: string, provider?: string): void {
    this.model = model;
    
    if (provider) {
      this.provider = provider;
      this.client = createOpenAIClient({
        provider: this.provider,
        timeout: this.timeout,
      });
    }

    logInfo(`Model updated: ${this.provider}/${this.model}`);
  }

  /**
   * Update approval policy
   */
  public updateApprovalPolicy(policy: ApprovalPolicy): void {
    this.approvalPolicy = policy;
    logInfo(`Approval policy updated: ${policy}`);
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<AgentLoopConfig>): void {
    if (config.model) {
      this.model = config.model;
    }

    if (config.provider) {
      this.provider = config.provider;
      this.client = createOpenAIClient({
        provider: this.provider,
        timeout: this.timeout,
      });
    }

    if (config.approvalPolicy) {
      this.approvalPolicy = config.approvalPolicy;
    }

    if (config.maxIterations) {
      this.maxIterations = config.maxIterations;
    }

    if (config.timeout) {
      this.timeout = config.timeout;
    }

    if (config.debug !== undefined) {
      this.debug = config.debug;
    }

    logInfo(`Agent loop configuration updated: ${this.provider}/${this.model}`);
  }

  /**
   * Clear conversation transcript
   */
  public clearTranscript(): void {
    this.transcript = [];
    logInfo('Conversation transcript cleared');
  }

  /**
   * Get conversation statistics
   */
  public getStats(): {
    messageCount: number;
    thinkingTime: number;
    modelInfo: { model: string; provider: string };
  } {
    return {
      messageCount: this.transcript.length,
      thinkingTime: this.cumulativeThinkingMs,
      modelInfo: {
        model: this.model,
        provider: this.provider,
      },
    };
  }

  /**
   * Abort pending operations
   */
  public abort(): void {
    logInfo('Aborting agent loop operations');
    this.pendingAborts.clear();
    this.emit('abort');
  }
}
