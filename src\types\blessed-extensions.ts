/**
 * Blessed.js Type Extensions
 *
 * Custom type definitions and extensions for blessed.js to fix
 * TypeScript compilation issues and add missing properties.
 */

import blessed from 'blessed';

// Re-export commonly used types with correct names
export type BlessedStyle = blessed.Widgets.Types.TStyle;
export type BlessedBorder = blessed.Widgets.Border;
export type BlessedKeyEvent = blessed.Widgets.Events.IKeyEventArg;
export type BlessedElement = blessed.Widgets.BlessedElement;
export type BlessedScreen = blessed.Widgets.Screen;
export type BlessedScreenOptions = blessed.Widgets.IScreenOptions;

// Extended interfaces to add missing properties
export interface ExtendedListElement extends blessed.Widgets.ListElement {
  selected: number;
  focused: boolean;
}

export interface ExtendedTextareaElement extends blessed.Widgets.TextareaElement {
  focused: boolean;
  blur(): void;
}

export interface ExtendedBoxElement extends blessed.Widgets.BoxElement {
  padding?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  scrollable?: boolean;
  alwaysScroll?: boolean;
  blur?(): void;
}

export interface ExtendedScreen extends blessed.Widgets.Screen {
  saveCursor?(): void;
  restoreCursor?(): void;
  screenshot(): string;
}

// Form element with proper generic
export type BlessedForm = blessed.Widgets.FormElement<any>;

// Position helpers
export interface NumericPosition {
  top: number;
  left: number;
  width: number;
  height: number;
}

// Utility type for converting TPosition to number
export function toNumber(value: blessed.Widgets.Types.TPosition): number {
  if (typeof value === 'number') {
    return value;
  }
  if (typeof value === 'string') {
    if (value === 'center') {
      return 0; // Will need context-specific handling
    }
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
}

// Type guards
export function isExtendedListElement(element: any): element is ExtendedListElement {
  return element && typeof element.selected !== 'undefined';
}

export function isExtendedTextareaElement(element: any): element is ExtendedTextareaElement {
  return element && typeof element.blur === 'function';
}

export function isExtendedBoxElement(element: any): element is ExtendedBoxElement {
  return element && typeof element.scrollable !== 'undefined';
}

// Cursor type with proper color property
export interface ExtendedCursor extends blessed.Widgets.Types.TCursor {
  color: string;
}

// List options with proper typing
export interface ExtendedListOptions extends Omit<blessed.Widgets.ListOptions<blessed.Widgets.ListElementStyle>, 'items'> {
  items?: (string | { text: string; value?: any })[];
}
